apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: apisix
  namespace: apisix
spec:
  values:
    # Configurazione specifica per UAT
    apisix:
      replicaCount: 1  # Ridotto per UAT
      # Configurazione SSL con certificato esistente
      ssl:
        enabled: true
        existingCert: "irideservice-wildcard-tls"
        certFilename: "tls.crt"
        keyFilename: "tls.key"
        
    # Configurazione gateway per HTTPS
    gateway:
      type: NodePort
      tls:
        enabled: true
        existingSecret: "irideservice-wildcard-tls"
        certFilename: "tls.crt"
        keyFilename: "tls.key"
      # Configurazione porte NodePort specifiche
      http:
        enabled: true
        servicePort: 80
        containerPort: 9080
        nodePort: 30080
      https:
        enabled: true
        servicePort: 443
        containerPort: 9443
        nodePort: 30443
        
    # Dashboard come servizio interno (sarà esposto tramite ApisixRoute)
    dashboard:
      enabled: true
      service:
        type: ClusterIP  # Interno, esposto tramite APISIX
        port: 9000
      # Configurazione autenticazione base
      config:
        authentication:
          secret: dashboard-secret-key
          expire_time: 3600
          users:
            - username: admin
              password: iehrbiw4567
            - username: thux
              password: PQWE8kjhg
        
    # ETCD ridotto per UAT
    etcd:
      enabled: true
      replicaCount: 1  # Ridotto per UAT
      auth:
        rbac:
          create: false
      persistence:
        enabled: true
        size: 8Gi
        
    # Ingress Controller configurato per UAT
    ingress-controller:
      enabled: true
      replicaCount: 1
      config:
        apisix:
          serviceNamespace: apisix