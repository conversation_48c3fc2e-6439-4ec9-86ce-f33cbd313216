apiVersion: v1
kind: Service
metadata:
  name: apisix-gateway-loadbalancer
  namespace: apisix
  annotations:
    metallb.universe.tf/loadBalancer-class: "metallb"
spec:
  type: LoadBalancer
  selector:
    app.kubernetes.io/name: apisix
    app.kubernetes.io/instance: apisix
  ports:
  - name: http
    port: 80
    targetPort: 9080
    protocol: TCP
  - name: https
    port: 443
    targetPort: 9443
    protocol: TCP