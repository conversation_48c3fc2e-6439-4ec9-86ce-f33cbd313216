apiVersion: apisix.apache.org/v2
kind: ApisixRoute
metadata:
  name: apisix-dashboard-route
  namespace: apisix
spec:
  http:
  - name: dashboard-route
    match:
      hosts:
      - dashboard-apisix.irideservices.earth
      paths:
      - /*
    backends:
    - serviceName: apisix-dashboard
      servicePort: 9000
      weight: 100
    plugins:
    - name: cors
      enable: true
      config:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "*"
        allow_credentials: true
    - name: proxy-rewrite
      enable: true
      config:
        use_real_request_uri_unsafe: true
---
apiVersion: apisix.apache.org/v2
kind: ApisixTls
metadata:
  name: dashboard-tls
  namespace: apisix
spec:
  hosts:
  - dashboard-apisix.irideservices.earth
  secret:
    name: irideservice-wildcard-tls
    namespace: apisix