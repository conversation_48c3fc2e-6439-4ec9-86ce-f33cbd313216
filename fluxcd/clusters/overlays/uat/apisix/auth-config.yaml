apiVersion: v1
kind: Secret
metadata:
  name: apisix-dashboard-config
  namespace: apisix
type: Opaque
stringData:
  conf.yaml: |
    conf:
      listen:
        host: 0.0.0.0
        port: 9000
      etcd:
        endpoints:
          - apisix-etcd:2379
      log:
        error_log:
          level: warn
          file_path: logs/error.log
        access_log:
          file_path: logs/access.log
      security:
        content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; frame-src *"
      authentication:
        secret: dashboard-secret-key
        expire_time: 3600
        users:
          - username: admin
            password: iehrbiw4567
          - username: thux
            password: PQWE8kjhg