# APISIX UAT Configuration

## Configurazione attuale

1. **LoadBalancer Service**: `apisix-gateway-loadbalancer` su IP ************
2. **NodePort Service**: `apisix-gateway` sulle porte 30080 (HTTP) e 30443 (HTTPS)
3. **Dashboard**: Accessibile via `https://dashboard-apisix.irideservices.earth`

## Modifiche applicate

1. **Rimosso il default route conflittuale**: Il file `default-ssl-route.yaml` ora contiene solo la configurazione TLS wildcard
2. **Patch per NodePort**: Creato `apisix-nodeport-patch.yaml` per forzare le porte 30080/30443
3. **Semplificata configurazione dashboard**: Rimossi i volumi extra che potevano causare conflitti

## Per applicare le modifiche

```bash
# Forza la riconciliazione di Flux
flux reconcile kustomization flux-system --with-source

# Oppure applica direttamente
kubectl apply -k overlays/uat/apisix/
```

## Test della configurazione

1. **Test LoadBalancer**:
   ```bash
   curl -k https://************
   ```

2. **Test NodePort** (sostituire NODE_IP con l'IP di un nodo):
   ```bash
   curl -k https://NODE_IP:30443
   ```

3. **Test Dashboard**:
   ```bash
   curl -k https://dashboard-apisix.irideservices.earth
   # Oppure aprire nel browser
   ```

## Credenziali Dashboard

- Username: `admin` Password: `iehrbiw4567`
- Username: `thux` Password: `PQWE8kjhg`

## Troubleshooting

Se ricevi "Service Temporarily Unavailable":

1. Verifica che i pod siano running:
   ```bash
   kubectl get pods -n apisix
   ```

2. Controlla i log di APISIX:
   ```bash
   kubectl logs -n apisix deployment/apisix
   ```

3. Verifica le route:
   ```bash
   kubectl get apisixroute -n apisix
   kubectl describe apisixroute dashboard-route -n apisix
   ```

4. Verifica che il certificato TLS esista:
   ```bash
   kubectl get secret irideservice-wildcard-tls -n apisix