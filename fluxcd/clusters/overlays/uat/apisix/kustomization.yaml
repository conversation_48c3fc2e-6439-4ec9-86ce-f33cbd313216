apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
metadata:
  name: apisix-uat
resources:
  - ../../../base/infrastructure/apisix
  - dashboard-route.yaml
  - apisix-loadbalancer.yaml
  - default-ssl-route.yaml

patches:
  - path: helmrelease-patch.yaml
    target:
      kind: HelmRelease
      name: apisix
  - path: apisix-nodeport-patch.yaml
    target:
      kind: Service
      name: apisix-gateway