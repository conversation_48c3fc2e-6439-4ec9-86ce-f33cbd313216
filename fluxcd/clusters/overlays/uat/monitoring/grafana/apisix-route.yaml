apiVersion: apisix.apache.org/v2
kind: ApisixRoute
metadata:
  name: grafana-route
  namespace: monitoring
spec:
  http:
  - name: grafana-http-route
    match:
      hosts:
      - uat-grafana-ssc.irideservices.earth
      paths:
      - /*
    backends:
    - serviceName: grafana
      servicePort: 80
      weight: 100
    plugins:
    - name: cors
      enable: true
      config:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "*"
        allow_credentials: true
    - name: proxy-rewrite
      enable: true
      config:
        use_real_request_uri_unsafe: true
---
apiVersion: apisix.apache.org/v2
kind: ApisixTls
metadata:
  name: grafana-tls
  namespace: monitoring
spec:
  hosts:
  - uat-grafana-ssc.irideservices.earth
  secret:
    name: irideservice-wildcard-tls
    namespace: monitoring