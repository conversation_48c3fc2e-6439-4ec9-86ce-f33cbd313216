loki:
  useTestSchema: true
  auth_enabled: false
  
  storage:
    type: filesystem
    filesystem:
      chunks_directory: /var/loki/chunks
      rules_directory: /var/loki/rules

# Disabilita monitoring che causa l'errore PodLogs
monitoring:
  selfMonitoring:
    enabled: false
    grafanaAgent:
      installOperator: false
  dashboards:
    enabled: false
  rules:
    enabled: false
  serviceMonitor:
    enabled: false

deploymentMode: SingleBinary

singleBinary:
  replicas: 1
  persistence:
    enabled: true
    size: 10Gi
    accessModes:
      - ReadWriteOnce
    storageClassName: "longhorn"

# Disabilita tutti gli altri componenti
backend:
  replicas: 0
read:
  replicas: 0
write:
  replicas: 0
ingester:
  replicas: 0
querier:
  replicas: 0
queryFrontend:
  replicas: 0
queryScheduler:
  replicas: 0
distributor:
  replicas: 0
compactor:
  replicas: 0
indexGateway:
  replicas: 0
bloomCompactor:
  replicas: 0
bloomGateway:
  replicas: 0

gateway:
  verboseLogging: false
  nginxConfig:
    resolver: "rke2-coredns-rke2-coredns.kube-system.svc.cluster.local valid=30s"

minio:
  enabled: false

gateway:
  verboseLogging: false
  nginxConfig:
    resolver: "rke2-coredns-rke2-coredns.kube-system.svc.cluster.local valid=30s"