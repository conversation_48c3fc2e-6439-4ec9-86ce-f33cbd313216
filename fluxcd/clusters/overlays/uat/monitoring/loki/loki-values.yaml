loki:
  useTestSchema: true
  
  storage:
    type: filesystem
    bucketNames:
      chunks: loki-chunks
      ruler: loki-ruler  
      admin: loki-admin
    filesystem:
      chunks_directory: /var/loki/chunks
      rules_directory: /var/loki/rules

deploymentMode: SingleBinary

singleBinary:
  replicas: 1

# AZZERA ESPLICITAMENTE tutti i componenti SimpleScalable
backend:
  replicas: 0
read:
  replicas: 0
write:
  replicas: 0
ingester:
  replicas: 0
querier:
  replicas: 0
queryFrontend:
  replicas: 0
queryScheduler:
  replicas: 0
distributor:
  replicas: 0
compactor:
  replicas: 0
indexGateway:
  replicas: 0
bloomCompactor:
  replicas: 0
bloomGateway:
  replicas: 0

minio:
  enabled: false