loki:
  storage:
    type: filesystem
    filesystem:
      chunks_directory: /var/loki/chunks
      rules_directory: /var/loki/rules

  schemaConfig:
    configs:
      - from: "2025-06-10"  # Data FUTURA
        store: tsdb
        object_store: filesystem
        schema: v13
        index:
          prefix: loki_index_
          period: 24h

  auth_enabled: false

  config: |
    common:
      path_prefix: /var/loki
    
    storage_config:
      tsdb_shipper:
        active_index_directory: /var/loki/tsdb-index
        cache_location: /var/loki/tsdb-cache
    
    compactor:
      working_directory: /var/loki/compactor
    
    ingester:
      chunk_encoding: snappy
    querier:
      max_concurrent: 3

gateway:
  verboseLogging: false
  nginxConfig:
    resolver: "rke2-coredns-rke2-coredns.kube-system.svc.cluster.local valid=30s"

deploymentMode: SingleBinary

backend:
  replicas: 0
read:
  replicas: 0
write:
  replicas: 0

minio:
  enabled: false

singleBinary:
  replicas: 1
  persistence:
    enabled: true
    size: 10Gi
    accessModes:
      - ReadWriteOnce
    storageClassName: "longhorn"

ingester:
  replicas: 0
querier:
  replicas: 0
queryFrontend:
  replicas: 0
queryScheduler:
  replicas: 0
distributor:
  replicas: 0
compactor:
  replicas: 0
indexGateway:
  replicas: 0
bloomCompactor:
  replicas: 0
bloomGateway:
  replicas: 0