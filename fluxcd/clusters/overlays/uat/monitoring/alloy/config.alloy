logging {
  level = "info"
  format = "logfmt"
}

// ============================================================================
// Kubernetes pod logs - https://grafana.com/docs/alloy/latest/collect/logs-in-kubernetes/#pods-logs
// ============================================================================

// discovery.kubernetes allows you to find scrape targets from Kubernetes resources.
// It watches cluster state and ensures targets are continually synced with what is currently running in your cluster.
// Prima raccogli informazioni sui namespace
discovery.kubernetes "namespace" {
  role = "namespace"
}
// debug {
//   dump_relabeled = true
// }
// Poi raccogli i pod
discovery.kubernetes "pod" {
  role = "pod"
}
// discovery.relabel "add_project_labels" {
//   targets = discovery.kubernetes.pod.targets
//
//   // Valore di default è "thux" (verrà sovrascritto dalle regole successive se corrispondono)
//   rule {
//     replacement = "thux"
//     target_label = "project_name"
//   }
//
//   // Regole per i namespace
//   rule {
//     source_labels = ["__meta_kubernetes_namespace"]
//     regex = "g-iot-core"
//     replacement = "giot"
//     target_label = "project_name"
//   }
//
//   rule {
//     source_labels = ["__meta_kubernetes_namespace"]
//     regex = "g-iot-tenant-test"
//     replacement = "giot"
//     target_label = "project_name"
//   }
//
//   rule {
//     source_labels = ["__meta_kubernetes_namespace"]
//     regex = "monitoring"
//     replacement = "thux"
//     target_label = "project_name"
//   }
//
//   rule {
//     source_labels = ["__meta_kubernetes_namespace"]
//     regex = "tools"
//     replacement = "infra"
//     target_label = "project_name"
//   }
//
//   // Regole per le label Rancher
//   rule {
//     source_labels = ["__meta_kubernetes_pod_label_field_cattle_io_projectId"]
//     regex = "p-npnsv"
//     replacement = "gimasi"
//     target_label = "project_name"
//   }
//
//   rule {
//     source_labels = ["__meta_kubernetes_pod_label_field_cattle_io_projectId"]
//     regex = "p-6kqqd"
//     replacement = "essepaghe"
//     target_label = "project_name"
//   }
//
//   rule {
//     source_labels = ["__meta_kubernetes_pod_label_field_cattle_io_projectId"]
//     regex = "p-dwjjv"
//     replacement = "thux-apps"
//     target_label = "project_name"
//   }
// }
// discovery.relabel rewrites the label set of the input targets by applying one or more relabeling rules.
// If no rules are defined, then the input targets are exported as-is.
// discovery.relabel "pod_logs" {
//   targets = discovery.relabel.add_project_labels.output
//
//   // Label creation - "namespace" field from "__meta_kubernetes_namespace"
//   rule {
//     source_labels = ["__meta_kubernetes_namespace"]
//     action = "replace"
//     target_label = "namespace"
//   }
//
//   // Label creation - "pod" field from "__meta_kubernetes_pod_name"
//   rule {
//     source_labels = ["__meta_kubernetes_pod_name"]
//     action = "replace"
//     target_label = "pod"
//   }
//
//   // Label creation - "container" field from "__meta_kubernetes_pod_container_name"
//   rule {
//     source_labels = ["__meta_kubernetes_pod_container_name"]
//     action = "replace"
//     target_label = "container"
//   }
//
//   // Label creation -  "app" field from "__meta_kubernetes_pod_label_app_kubernetes_io_name"
//   rule {
//     source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
//     action = "replace"
//     target_label = "app"
//   }
//
//   // Label creation -  "job" field from "__meta_kubernetes_namespace" and "__meta_kubernetes_pod_container_name"
//   // Concatenate values __meta_kubernetes_namespace/__meta_kubernetes_pod_container_name
//   rule {
//     source_labels = ["__meta_kubernetes_namespace", "__meta_kubernetes_pod_container_name"]
//     action = "replace"
//     target_label = "job"
//     separator = "/"
//     replacement = "$1"
//   }
//
//   // Label creation - "container" field from "__meta_kubernetes_pod_uid" and "__meta_kubernetes_pod_container_name"
//   // Concatenate values __meta_kubernetes_pod_uid/__meta_kubernetes_pod_container_name.log
//   rule {
//     source_labels = ["__meta_kubernetes_pod_uid", "__meta_kubernetes_pod_container_name"]
//     action = "replace"
//     target_label = "__path__"
//     separator = "/"
//     replacement = "/var/log/pods/*$1/*.log"
//   }
//
//   // Label creation -  "container_runtime" field from "__meta_kubernetes_pod_container_id"
//   rule {
//     source_labels = ["__meta_kubernetes_pod_container_id"]
//     action = "replace"
//     target_label = "container_runtime"
//     regex = "^(\\S+):\\/\\/.+$"
//     replacement = "$1"
//   }
// }

// loki.source.kubernetes tails logs from Kubernetes containers using the Kubernetes API.
loki.source.kubernetes "pod_logs" {
  targets    = discovery.kubernetes.pod.targets // MODIFICATO: Rimosso .output da discovery.relabel.pod_logs e puntato a discovery.kubernetes.pod.targets
  // forward_to = [loki.relabel.pod_logs.receiver] // COMMENTATO: Non più necessario
  forward_to = [loki.write.default_loki.receiver] // MODIFICATO: Inoltra direttamente a loki.write
}

// loki.process receives log entries from other Loki components, applies one or more processing stages,
// and forwards the results to the list of receivers in the component’s arguments.
// loki.relabel "pod_logs" {
//   rule {
//     target_label = "cluster"
//     replacement  = "[prod]"
//   }
//
//   forward_to = [loki.write.default_loki.receiver]
// }

// ============================================================================
// Kubernetes cluster events https://grafana.com/docs/alloy/latest/collect/logs-in-kubernetes/#kubernetes-cluster-events
// ============================================================================

// loki.source.kubernetes_events tails events from the Kubernetes API and converts them
// into log lines to forward to other Loki components.
loki.source.kubernetes_events "cluster_events" {
  job_name   = "integrations/kubernetes/eventhandler"
  log_format = "logfmt"
  forward_to = [
    loki.process.cluster_events.receiver,
  ]
}

loki.process "cluster_events" {

  stage.static_labels {
    values = {
      cluster = "prod",
    }
  }

  stage.labels {
    values = {
      kubernetes_cluster_events = "job",
    }
  }

  forward_to = [loki.write.default_loki.receiver]
}

// ============================================================================
// Node metrics
// ============================================================================
prometheus.exporter.unix "node_metrics" {
  enable_collectors = ["uname"]
}

prometheus.scrape "node_metrics_scrape" {
  targets = prometheus.exporter.unix.node_metrics.targets

  scrape_interval = "30s"
  job_name = "node-exporter"

  forward_to = [ prometheus.remote_write.default_mimir.receiver ] // MODIFICATO: Inoltra direttamente a mimir
}

// prometheus.relabel "node_metrics_scrape" {
//
//   rule {
//     replacement = env("HOSTNAME")
//     target_label = "nodename"
//   }
//
//   rule {
//     replacement = "dev"
//     target_label = "cluster"
//   }
//
//   rule {
//     // The default job name is "integrations/node_exporter" and needs to be replaced
//     replacement = "node-exporter"
//     target_label = "job"
//   }
//
//   forward_to = [ prometheus.relabel.normalizer.receiver ]
// }

// ============================================================================
// Kube State metrics - https://github.com/grafana/alloy-modules/tree/main/modules/kubernetes/kube-state-metrics
// ============================================================================
import.git "ksm" {
  repository = "https://github.com/grafana/alloy-modules.git"
  revision = "main"
  path = "modules/kubernetes/kube-state-metrics/metrics.alloy"
  pull_frequency = "900m"
}

// get the targets
ksm.kubernetes "targets" {
  // Nessun parametro namespaces per scoprire dinamicamente tutti i namespace
}

// scrape the targets
ksm.scrape "metrics" {
  targets = ksm.kubernetes.targets.output
  job_label = "kube_state_metrics"
  scrape_interval = "30s"

  forward_to = [ prometheus.remote_write.default_mimir.receiver ] // MODIFICATO: Inoltra direttamente a mimir
}

// ============================================================================
// Pod metrics - cAdvisor - https://pokgak.com/articles/scrape-cadvisor-alloy/
// ============================================================================
discovery.kubernetes "nodes" {
  role = "node"
}

// discovery.relabel "cadvisor_nodes" {
//   targets = discovery.kubernetes.nodes.targets
//
//   rule {
//     replacement   = "/metrics/cadvisor"
//     target_label  = "__metrics_path__"
//   }
// }

prometheus.scrape "k8s_node_cadvisor" {
  //job_name   = "integrations/kubernetes/cadvisor"
  targets    = discovery.kubernetes.nodes.targets // MODIFICATO: Rimosso .output da discovery.relabel.cadvisor_nodes e puntato a discovery.kubernetes.nodes.targets
  honor_labels = true
  scrape_interval = "30s"
  scheme = "https"
  bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
  tls_config {
    ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
  }

  forward_to = [prometheus.remote_write.default_mimir.receiver] // MODIFICATO: Inoltra direttamente a mimir
}

// ============================================================================
// Postgrsql https://grafana.com/docs/alloy/latest/reference/components/prometheus/prometheus.exporter.postgres/#collect-metrics-from-a-postgresql-server
// ============================================================================
// prometheus.exporter.postgres "g_iot_core" {
//   data_source_names = ["******************************************/g_iot_core?sslmode=disable"]
// }

// prometheus.exporter.postgres "g_iot_core_geolocation" {
//   data_source_names = ["******************************************/g_iot_core_geolocation?sslmode=disable"]
// }

// prometheus.exporter.postgres "g_iot_tenant_test" {
//   data_source_names = ["******************************************/g_iot_tenant_test?sslmode=disable"]
// }

// prometheus.scrape "postgresql" {
//   targets    =  array.concat(
//     prometheus.exporter.postgres.g_iot_core.targets,
//     prometheus.exporter.postgres.g_iot_core_geolocation.targets,
//     prometheus.exporter.postgres.g_iot_tenant_test.targets,
//   )
//   scrape_interval = "30s"
//   forward_to = [prometheus.relabel.drop_pgsql_settings.receiver]
// }

// prometheus.relabel "drop_pgsql_settings" {
//   // We don't want to log pg_settings_* family of metrics
//   rule {
//     source_labels = [ "__name__" ]
//     regex = "pg_settings_*"
//     action = "drop"
//   }

//   rule {
//     replacement = "g-iot-core"
//     target_label = "namespace"
//   }

//   forward_to = [prometheus.relabel.normalizer.receiver]
// }


// ============================================================================
// Per-service metrics using serviceMonitor - https://medium.com/@stepanvrany/i-replaced-several-kubernetes-components-with-grafana-agent-flow-heres-how-74ecdc60bffe
// ============================================================================

prometheus.operator.servicemonitors "services" {
  namespaces = [ "g-iot-core", "g-iot-tenant-test", "monitoring", "tools", "traefik" ]
  forward_to = [prometheus.remote_write.default_mimir.receiver] // MODIFICATO: Inoltra direttamente a mimir
}

prometheus.operator.podmonitors "podmonitors" {
  namespaces = [ "g-iot-core", "g-iot-tenant-test", "monitoring", "tools", "traefik" ]
  forward_to = [prometheus.remote_write.default_mimir.receiver] // MODIFICATO: Inoltra direttamente a mimir
}

// ============================================================================
// Add "cluster" label to all metrics, drop ones that we don't want
// ============================================================================

// prometheus.relabel "normalizer" {
//
//   rule {
//     source_labels = [ "__name__" ]
//     regex = "erlang_vm_allocators*"
//     action = "drop"
//   }
//
//   rule {
//     source_labels = [ "__name__" ]
//     regex = "container_tasks_state*"
//     action = "drop"
//   }
//
//   rule {
//     replacement = "prod"
//     target_label = "cluster"
//   }
//
//   // forward_to = [prometheus.relabel.add_metrics_project_labels.receiver] // COMMENTATO
//   forward_to = [prometheus.remote_write.default_mimir.receiver] // MODIFICATO: Inoltra direttamente a mimir
//
// }
//
// prometheus.relabel "add_metrics_project_labels" {
//   // Regole per project_name basate su namespace
//   rule {
//     source_labels = ["namespace"]
//     regex = "g-iot-core|g-iot-tenant-test"
//     replacement = "giot"
//     target_label = "project_name"
//   }
//
//   rule {
//     source_labels = ["namespace"]
//     regex = "monitoring"
//     replacement = "thux"
//     target_label = "project_name"
//   }
//
//   rule {
//     source_labels = ["namespace"]
//     regex = "tools"
//     replacement = "infra"
//     target_label = "project_name"
//   }
//
//   // Default per chi non ha namespace o non corrisponde alle regole
//   rule {
//     replacement = "unknown"
//     target_label = "project_name"
//   }
//
//   forward_to = [prometheus.remote_write.default_mimir.receiver]
// }
// ============================================================================
// Write endpoints
// ============================================================================

loki.write "default_loki" {
  endpoint {
    url = "http://loki-write.monitoring.svc.cluster.local:3100/loki/api/v1/push"
    tenant_id = "prod"
  }
}

prometheus.remote_write "default_mimir" {
  endpoint {
    url = "http://mimir-nginx.monitoring.svc.cluster.local:80/api/v1/push"
    queue_config {
      sample_age_limit = "300s"
    }
  }
}
