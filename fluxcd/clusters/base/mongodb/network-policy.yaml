apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mongodb-network-policy
  namespace: mongodb-operator
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: percona-server-mongodb
  policyTypes:
    - Ingress
    - Egress
  ingress:
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: percona-server-mongodb
      ports:
        - protocol: TCP
          port: 27017
  egress:
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: percona-server-mongodb
      ports:
        - protocol: TCP
          port: 27017