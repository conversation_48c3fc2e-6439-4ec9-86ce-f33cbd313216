apiVersion: psmdb.percona.com/v1
kind: PerconaServerMongoDB
metadata:
  name: dbprova
  namespace: mongodb-operator
spec:
  crVersion: "1.15.0"
  image: percona/percona-server-mongodb:latest # AGGIUNGI QUESTO - potresti voler specificare una versione esatta
  imagePullPolicy: Always
  secrets:
    users: mongodb-secrets
  replsets:
  - name: rs0
    size: 1 # Iniziamo con 1 per un db singolo semplice come richiesto inizialmente
    volumeSpec:
      persistentVolumeClaim:
        resources:
          requests:
            storage: 10Gi
        storageClassName: longhorn
        accessModes: [ "ReadWriteOnce" ]
    expose:
      enabled: false # Non esporre esternamente per ora
  # Rimuoviamo la sezione 'storage' globale dato che è per replset