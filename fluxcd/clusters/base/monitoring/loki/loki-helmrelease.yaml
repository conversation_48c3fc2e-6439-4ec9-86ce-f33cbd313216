apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: loki
spec:
  interval: 10m
  timeout: 5m
  chart:
    spec:
      chart: loki
      sourceRef:
        kind: HelmRepository
        name: grafana
      interval: 24h
  releaseName: loki
  install:
    remediation:
      retries: 3
  upgrade:
    remediation:
      retries: 3
  driftDetection:
    mode: enabled
    ignore:
    - paths: ["/spec/replicas"]
      target:
        kind: Deployment
  valuesFrom:
    - kind: ConfigMap
      name: loki-values
