apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: kube-state-metrics
spec:
  interval: 600m
  timeout: 5m
  chart:
    spec:
      chart: kube-state-metrics
      sourceRef:
        kind: HelmRepository
        name: prometheus-community
      interval: 24h
  releaseName: kube-state-metrics
  install:
    remediation:
      retries: 3
  upgrade:
    remediation:
      retries: 3
  driftDetection:
    mode: enabled
    ignore:
    - paths: ["/spec/replicas"]
      target:
        kind: Deployment
#  values:
#    namespaces:
#      - g-iot-core
#      - g-iot-tenant-test
#      - monitoring
#      - tools
