# base/postgres/secrets.yaml.template
apiVersion: v1
kind: Secret
metadata:
  name: pg-credentials
  namespace: test-flux-pg
type: Opaque
stringData:
  # Template - NON USARE IN PRODUZIONE
  POSTGRES_PASSWORD: "change-me-123"
  S3_SECRET_KEY: "change-me-secret-key-xxx"


# Da fare una volta prima del deploy
# kubectl create secret generic pg-credentials \
#   --namespace test-flux-pg \
#   --from-literal=password=mypassword \
#   --from-literal=S3_SECRET_KEY=mysecretkey