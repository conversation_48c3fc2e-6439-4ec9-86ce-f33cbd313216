# base/postgres/helmrelease.yaml
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: cnpg
spec:
  interval: 5m
  chart:
    spec:
      chart: cluster
      version: "0.2.0"  
      sourceRef:
        kind: HelmRepository
        name: cnpg
        namespace: postgres-system 
  valuesFrom:
    - kind: ConfigMap
      name: pg-values-base
      valuesKey: values
    - kind: ConfigMap
      name: pg-values-overlay
      valuesKey: values
    - kind: Secret
      name: pg-credentials
      targetPath: backups.s3.secretKey
      valuesKey: S3_SECRET_KEY
