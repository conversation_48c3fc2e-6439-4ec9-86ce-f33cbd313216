apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: metallb
  namespace: metallb-system
spec:
  interval: 5m
  chart:
    spec:
      chart: metallb
      version: "0.13.10"
      sourceRef:
        kind: HelmRepository
        name: metallb
        namespace: metallb-system
  install:
    remediation:
      retries: 3
    crds: CreateReplace
  upgrade:
    remediation:
      retries: 3
    crds: CreateReplace
  values:
    # Configurazione base
    speaker:
      frr:
        enabled: false
    crds:
      enabled: true