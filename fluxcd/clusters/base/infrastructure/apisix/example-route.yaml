# Esempio di configurazione di una route APISIX
# Questo file è solo un esempio e non viene applicato automaticamente
apiVersion: apisix.apache.org/v2
kind: ApisixRoute
metadata:
  name: example-api-route
  namespace: apisix
spec:
  http:
  - name: api-route
    match:
      hosts:
      - api.example.com
      paths:
      - /api/*
    backends:
    - serviceName: backend-service
      servicePort: 8080
      weight: 100
    plugins:
    - name: cors
      enable: true
      config:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE"
        allow_headers: "*"
    - name: rate-limit
      enable: true
      config:
        rate: 100
        burst: 200
        key: "remote_addr"
---
# Esempio di configurazione upstream
apiVersion: apisix.apache.org/v2
kind: ApisixUpstream
metadata:
  name: backend-upstream
  namespace: apisix
spec:
  loadbalancer:
    type: roundrobin
  healthCheck:
    active:
      httpPath: /health
      healthy:
        interval: 2
        successes: 1
      unhealthy:
        interval: 1
        httpFailures: 2