apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: apisix
  namespace: apisix
spec:
  interval: 5m
  chart:
    spec:
      chart: apisix
      version: "2.8.0"
      sourceRef:
        kind: HelmRepository
        name: apisix
        namespace: apisix
  install:
    remediation:
      retries: 3
    crds: CreateReplace
  upgrade:
    remediation:
      retries: 3
    crds: CreateReplace
  values:
    # Configurazione base APISIX
    apisix:
      image:
        repository: apache/apisix
        tag: "3.8.0-debian"
      replicaCount: 2
      
    # Dashboard APISIX
    dashboard:
      enabled: true
      image:
        repository: apache/apisix-dashboard
        tag: "3.0.1-alpine"
      
    # Ingress Controller
    ingress-controller:
      enabled: true
      image:
        repository: apache/apisix-ingress-controller
        tag: "1.8.0"
      
    # Gateway API
    gateway:
      type: LoadBalancer
      http:
        enabled: true
        servicePort: 80
        containerPort: 9080
        nodePort: 30080
      https:
        enabled: true
        servicePort: 443
        containerPort: 9443
        nodePort: 30443
      admin:
        enabled: true
        servicePort: 9180
        containerPort: 9180
        
    # ETCD per APISIX
    etcd:
      enabled: true
      replicaCount: 3
      auth:
        rbac:
          create: false