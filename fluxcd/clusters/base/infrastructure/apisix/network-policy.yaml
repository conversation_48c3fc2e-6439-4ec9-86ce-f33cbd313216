apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: apisix-network-policy
  namespace: apisix
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: apisix
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector: {}
    ports:
    - protocol: TCP
      port: 9080  # HTTP
    - protocol: TCP
      port: 9443  # HTTPS
    - protocol: TCP
      port: 9180  # Admin API
  - from: []  # Consenti traffico esterno per LoadBalancer
    ports:
    - protocol: TCP
      port: 9080  # HTTP
    - protocol: TCP
      port: 9443  # HTTPS
  egress:
  - {}  # Consenti tutto il traffico in uscita
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: apisix-dashboard-network-policy
  namespace: apisix
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: apisix-dashboard
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # Consenti traffico esterno per LoadBalancer
    ports:
    - protocol: TCP
      port: 9000  # Dashboard
  egress:
  - to:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: apisix
    ports:
    - protocol: TCP
      port: 9180  # Admin API
  - {}  # Consenti tutto il traffico in uscita per altre dipendenze