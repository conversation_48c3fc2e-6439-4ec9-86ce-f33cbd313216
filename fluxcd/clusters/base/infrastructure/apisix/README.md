# APISIX API Gateway

Questa configurazione installa APISIX come API Gateway nel cluster Kubernetes usando Helm e FluxCD.

## Componenti

- **APISIX Gateway**: Il gateway API principale
- **APISIX Dashboard**: Interfaccia web per la gestione
- **APISIX Ingress Controller**: Controller per gestire le risorse Ingress
- **ETCD**: Database per la configurazione di APISIX

## Servizi Esposti

### Ambiente UAT
- **Gateway HTTP**: Porta 80 (LoadBalancer via MetalLB)
- **Gateway HTTPS**: Porta 443 (LoadBalancer via MetalLB)
- **Dashboard**: Accessibile tramite https://dashboard-apisix.irideservices.earth (ApisixRoute)
- **Admin API**: Porta 9180 (ClusterIP - solo interno)

## Configurazione

### Base
La configurazione base include:
- Namespace `apisix`
- HelmRepository per i chart APISIX
- HelmRelease con configurazione di base
- NetworkPolicy per la sicurezza

### Overlay UAT
L'overlay UAT personalizza:
- Riduce le repliche per l'ambiente di test
- Configura i servizi come LoadBalancer per MetalLB
- Configura ETCD con persistenza ridotta

## Accesso

Una volta deployato, APISIX sarà accessibile tramite:

```bash
# Verifica gli IP assegnati da MetalLB
kubectl get svc -n apisix

# Gateway API (LoadBalancer - servizio apisix-gateway-loadbalancer)
http://<GATEWAY_IP>:80
https://<GATEWAY_IP>:443

# Dashboard (tramite ApisixRoute)
https://dashboard-apisix.irideservices.earth

# Verifica le route configurate
kubectl get apisixroute -n apisix
kubectl get apisixtls -n apisix

# Verifica il servizio LoadBalancer
kubectl get svc apisix-gateway-loadbalancer -n apisix
```

## Gestione

Per gestire le route e le configurazioni, puoi usare:
1. **Dashboard Web**: Interfaccia grafica su porta 9000
2. **Admin API**: API REST su porta 9180 (solo interno)
3. **kubectl**: Gestione diretta delle risorse Kubernetes

## Monitoraggio

APISIX include metriche Prometheus integrate. Le metriche sono disponibili su:
- Gateway: `/apisix/prometheus/metrics`
- Admin API: `/metrics`