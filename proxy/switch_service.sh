#!/bin/bash

# Script per cambiare il servizio attivo tra HAProxy e Bunkerweb
# Deve essere eseguito su entrambi i nodi proxy

STATE_FILE="/etc/keepalived/service_state"
LOG_FILE="/var/log/keepalived/service_switch.log"

# Funzione di logging
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Funzione di help
show_help() {
    echo "Usage: $0 [haproxy|bunkerweb|status]"
    echo ""
    echo "Commands:"
    echo "  haproxy   - Switch to HAProxy"
    echo "  bunkerweb - Switch to Bunkerweb"
    echo "  status    - Show current service status"
    echo ""
    echo "Examples:"
    echo "  $0 haproxy    # Switch to HAProxy"
    echo "  $0 bunkerweb  # Switch to Bunkerweb"
    echo "  $0 status     # Show current status"
}

# Controlla i parametri
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

case "$1" in
    "haproxy")
        echo "haproxy" > "$STATE_FILE"
        log_message "Service switched to HAProxy"
        echo "Service switched to HAProxy"
        echo "Restarting keepalived to apply changes..."
        systemctl restart keepalived
        ;;
    "bunkerweb")
        echo "bunkerweb" > "$STATE_FILE"
        log_message "Service switched to Bunkerweb"
        echo "Service switched to Bunkerweb"
        echo "Restarting keepalived to apply changes..."
        systemctl restart keepalived
        ;;
    "status")
        if [ -f "$STATE_FILE" ]; then
            CURRENT_SERVICE=$(cat "$STATE_FILE" | tr -d '[:space:]')
            echo "Current service: $CURRENT_SERVICE"
            
            # Mostra lo stato dei servizi
            echo ""
            echo "Service status:"
            echo "  HAProxy: $(systemctl is-active haproxy)"
            echo "  Bunkerweb: $(systemctl is-active bunkerweb)"
            echo "  Keepalived: $(systemctl is-active keepalived)"
        else
            echo "No service state file found"
        fi
        ;;
    *)
        echo "Error: Unknown command '$1'"
        show_help
        exit 1
        ;;
esac
