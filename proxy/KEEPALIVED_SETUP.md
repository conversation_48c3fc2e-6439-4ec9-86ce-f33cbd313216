# Configurazione Keepalived per Proxy HA

## Panoramica

Questa configurazione implementa un setup di alta disponibilità con keepalived per due server proxy con le seguenti caratteristiche:

### Server Configuration
- **proxy-1**: ************ (ens160), *********** (ens192) - MASTER
- **proxy-2**: ************ (ens160), *********** (ens192) - BACKUP

### Virtual IPs (VIP)
- **VIP 1**: ************ (ens160)
- **VIP 2**: *********** (ens192)

## Installazione

### 1. Copia i file di configurazione

**Su proxy-1:**
```bash
cd proxy-1/
sudo rsync -a etc/ /etc/
cd ..
sudo cp switch_service.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/switch_service.sh
```

**Su proxy-2:**
```bash
cd proxy-2/
sudo rsync -a etc/ /etc/
cd ..
sudo cp switch_service.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/switch_service.sh
```

### 2. Imposta i permessi degli script
```bash
sudo chmod +x /etc/keepalived/*.sh
```

### 3. Crea le directory di log
```bash
sudo mkdir -p /var/log/keepalived
sudo chown keepalived:keepalived /var/log/keepalived
```

### 4. Avvia keepalived
```bash
sudo systemctl enable keepalived
sudo systemctl start keepalived
```

## Gestione dei Servizi

### Controllo dello stato
```bash
# Mostra il servizio attualmente configurato
switch_service.sh status

# Controlla lo stato di keepalived
sudo systemctl status keepalived

# Visualizza i log
sudo tail -f /var/log/keepalived/keepalived.log
```

### Cambio tra HAProxy e Bunkerweb

**Per passare a HAProxy:**
```bash
sudo switch_service.sh haproxy
```

**Per passare a Bunkerweb:**
```bash
sudo switch_service.sh bunkerweb
```

**Nota:** Il comando deve essere eseguito su entrambi i server per mantenere la coerenza.

## File di Configurazione

### File di Stato
- `/etc/keepalived/service_state` - Contiene il servizio attivo (haproxy/bunkerweb)

### Script di Monitoraggio
- `check_service.sh` - Controlla lo stato del servizio attivo
- `master.sh` - Eseguito quando il nodo diventa MASTER
- `backup.sh` - Eseguito quando il nodo diventa BACKUP  
- `fault.sh` - Eseguito in caso di fault

### Log Files
- `/var/log/keepalived/keepalived.log` - Log delle transizioni di stato
- `/var/log/keepalived/check_service.log` - Log dei controlli di salute
- `/var/log/keepalived/service_switch.log` - Log dei cambi di servizio

## Comportamento

1. **Avvio**: Di default viene utilizzato HAProxy
2. **MASTER**: Solo il nodo MASTER ha i servizi attivi e risponde sui VIP
3. **BACKUP**: Il nodo BACKUP ha tutti i servizi fermi
4. **Failover**: In caso di problemi, il BACKUP diventa MASTER automaticamente
5. **Transizione**: Per cambiare da HAProxy a Bunkerweb, modificare il file di stato e riavviare keepalived

## Troubleshooting

### Problema: Entrambi i proxy hanno i VIP

Se entrambi i proxy hanno i VIP contemporaneamente, seguire questi passi:

1. **Ferma keepalived su entrambi i nodi:**
```bash
sudo systemctl stop keepalived
```

2. **Rimuovi manualmente i VIP (se presenti):**
```bash
sudo ip addr del ************/24 dev ens160 2>/dev/null || true
sudo ip addr del ***********/24 dev ens192 2>/dev/null || true
```

3. **Verifica i permessi degli script:**
```bash
sudo chmod +x /etc/keepalived/*.sh
```

4. **Riavvia keepalived prima su proxy-1, poi su proxy-2:**
```bash
# Su proxy-1
sudo systemctl start keepalived
# Aspetta 10 secondi
# Su proxy-2
sudo systemctl start keepalived
```

### Verifica della configurazione
```bash
sudo keepalived -t -f /etc/keepalived/keepalived.conf
```

### Controllo degli IP virtuali
```bash
ip addr show ens160
ip addr show ens192
```

### Debug keepalived
```bash
sudo journalctl -u keepalived -f
```

### Test di failover
```bash
# Su proxy-1 (MASTER)
sudo systemctl stop keepalived

# Verifica che proxy-2 diventi MASTER
# Su proxy-2
ip addr show ens160 | grep ************
```

## Note Importanti

- I servizi proxy (HAProxy/Bunkerweb) vengono avviati solo sul nodo MASTER
- La transizione tra servizi richiede il riavvio di keepalived
- Assicurarsi che entrambi i servizi siano configurati correttamente prima del deployment
- Il file di stato deve essere sincronizzato manualmente tra i nodi
