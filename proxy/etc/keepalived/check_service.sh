#!/bin/bash

# Script per controllare se HAProxy o Bunkerweb è attivo
# Legge il file di stato per decidere quale servizio controllare

STATE_FILE="/etc/keepalived/service_state"
LOG_FILE="/var/log/keepalived/check_service.log"

# Crea il file di stato se non esiste (default: haproxy)
if [ ! -f "$STATE_FILE" ]; then
    echo "haproxy" > "$STATE_FILE"
fi

# Legge lo stato corrente
SERVICE=$(cat "$STATE_FILE" | tr -d '[:space:]')

# Funzione di logging
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

case "$SERVICE" in
    "haproxy")
        # Controlla HAProxy
        if systemctl is-active --quiet haproxy; then
            log_message "HAProxy is running"
            exit 0
        else
            log_message "HAProxy is not running"
            exit 1
        fi
        ;;
    "bunkerweb")
        # Controlla Bunkerweb
        if systemctl is-active --quiet bunkerweb; then
            log_message "Bunkerweb is running"
            exit 0
        else
            log_message "Bunkerweb is not running"
            exit 1
        fi
        ;;
    *)
        log_message "Unknown service state: $SERVICE"
        exit 1
        ;;
esac
