global
    log 127.0.0.1:514 local0
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy
    daemon

defaults
    mode tcp
    log global
    option tcplog
    option dontlognull
    option log-health-checks
    retries 3
    timeout connect 10s
    timeout client 1m
    timeout server 1m
    timeout check 10s
    maxconn 3000

# Frontend per traffico HTTPS esterno (porta 443) con SNI routing
frontend rancher_https_external
    bind ************:443
    mode tcp
    option tcplog
    tcp-request inspect-delay 5s
    tcp-request content accept if { req_ssl_hello_type 1 }
    
    # Routing basato su SNI per interfaccia Rancher
    use_backend rancher_k3s_tcp_backend if { req_ssl_sni -i rancher.planetek.thux.dev }
    use_backend rancher_k3s_tcp_backend if { req_ssl_sni -i uat-rancher-ssc.irideservices.earth }
    
    # Default backend per tutto il resto → cluster RKE2
    default_backend rancher_rke2_tcp_backend

# Frontend per traffico HTTP esterno (porta 80) 
frontend rancher_http_external
    bind ************:80
    mode http
    option httplog
    
    # Routing basato su Host header per interfaccia Rancher
    use_backend rancher_k3s_http_backend if { hdr(host) -i rancher.planetek.thux.dev }
    use_backend rancher_k3s_http_backend if { hdr(host) -i uat-rancher-ssc.irideservices.earth }
    
    # Default backend per tutto il resto → cluster RKE2
    default_backend rancher_rke2_http_backend

# Frontend per traffico interno TCP (interfaccia rancher per nodi RKE2)
frontend rancher_tcp_internal
    bind ***********:443
    mode tcp
    option tcplog
    default_backend rancher_k3s_tcp_backend

# Backend TCP per interfaccia Rancher (cluster K3s)
backend rancher_k3s_tcp_backend
    mode tcp
    balance roundrobin
    option tcp-check
    # Nodi K3s per interfaccia Rancher
    server k3s-1 ************:30443 check
    server k3s-2 ************:30443 check
    server k3s-3 ************:30443 check

# Backend HTTP per interfaccia Rancher (cluster K3s) - per porta 80
backend rancher_k3s_http_backend
    mode http
    balance roundrobin
    option httpchk GET /
    # Nodi K3s per interfaccia Rancher
    server k3s-1 ************:30443 check ssl verify none
    server k3s-2 ************:30443 check ssl verify none
    server k3s-3 ************:30443 check ssl verify none

# Backend TCP per cluster RKE2 (default)
backend rancher_rke2_tcp_backend
    mode tcp
    balance roundrobin
    option tcp-check
    # Nodi RKE2 worker per ingress del cluster interno
    server rke2-wk-1 ***********1:30443 check
    server rke2-wk-2 ***********2:30443 check
    server rke2-wk-3 ***********3:30443 check

# Backend HTTP per cluster RKE2 (default) - per porta 80
backend rancher_rke2_http_backend
    mode http
    balance roundrobin
    option httpchk GET /
    # Nodi RKE2 worker per ingress del cluster interno
    server rke2-wk-1 ***********1:30443 check ssl verify none
    server rke2-wk-2 ***********2:30443 check ssl verify none
    server rke2-wk-3 ***********3:30443 check ssl verify none

# Stats page (opzionale, per monitoraggio)
frontend stats
    bind ***********:8404
    mode http
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
