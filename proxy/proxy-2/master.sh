#!/bin/bash

# Script eseguito quando il nodo diventa MASTER
# Avvia il servizio appropriato (HAProxy o Bunkerweb) basandosi sul file di stato

STATE_FILE="/etc/keepalived/service_state"
LOG_FILE="/var/log/keepalived/keepalived.log"

# Funzione di logging
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - MASTER - $1" >> "$LOG_FILE"
}

log_message "Becoming MASTER - Starting services"

# Legge il servizio da avviare
if [ -f "$STATE_FILE" ]; then
    SERVICE=$(cat "$STATE_FILE" | tr -d '[:space:]')
else
    SERVICE="haproxy"
    echo "haproxy" > "$STATE_FILE"
fi

case "$SERVICE" in
    "haproxy")
        log_message "Starting HAProxy service"
        systemctl start haproxy
        if [ $? -eq 0 ]; then
            log_message "HAProxy started successfully"
        else
            log_message "Failed to start HAProxy"
        fi
        # Assicurati che Bunkerweb sia fermo
        systemctl stop bunkerweb 2>/dev/null
        ;;
    "bunkerweb")
        log_message "Starting Bunkerweb service"
        systemctl start bunkerweb
        if [ $? -eq 0 ]; then
            log_message "Bunkerweb started successfully"
        else
            log_message "Failed to start Bunkerweb"
        fi
        # Assicurati che HAProxy sia fermo
        systemctl stop haproxy 2>/dev/null
        ;;
    *)
        log_message "Unknown service: $SERVICE - defaulting to HAProxy"
        systemctl start haproxy
        ;;
esac

# Invia notifica GARP per assicurare la propagazione ARP
arping -c 3 -A 10.200.200.3 -I ens160 &
arping -c 3 -A 172.16.20.3 -I ens192 &

log_message "MASTER transition completed"
