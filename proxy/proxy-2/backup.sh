#!/bin/bash

# Script eseguito quando il nodo diventa BACKUP
# Ferma i servizi proxy dato che solo il MASTER deve servire traffico

LOG_FILE="/var/log/keepalived/keepalived.log"

# Funzione di logging
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - BACKUP - $1" >> "$LOG_FILE"
}

log_message "Becoming BACKUP - Stopping services"

# Ferma entrambi i servizi
systemctl stop haproxy 2>/dev/null
systemctl stop bunkerweb 2>/dev/null

log_message "Services stopped - BACKUP transition completed"
