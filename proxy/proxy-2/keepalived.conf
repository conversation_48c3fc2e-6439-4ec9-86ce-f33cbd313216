! Configuration File for keepalived

global_defs {
   notification_email {
     <EMAIL>
   }
   notification_email_from <EMAIL>
   smtp_server localhost
   smtp_connect_timeout 30
   router_id PROXY_2
   vrrp_skip_check_adv_addr
   vrrp_strict
   vrrp_garp_interval 0
   vrrp_gna_interval 0
}

# Script per controllare HAProxy/Bunkerweb
vrrp_script chk_service {
    script "/etc/keepalived/check_service.sh"
    interval 2
    weight -2
    fall 3
    rise 2
}

# VRRP instance per ens160 (10.200.200.x)
vrrp_instance VI_1 {
    state BACKUP
    interface ens160
    virtual_router_id 51
    priority 100
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass planetek2024
    }
    virtual_ipaddress {
        ************/24
    }
    track_script {
        chk_service
    }
    notify_master "/etc/keepalived/master.sh"
    notify_backup "/etc/keepalived/backup.sh"
    notify_fault "/etc/keepalived/fault.sh"
}

# VRRP instance per ens192 (172.16.20.x)
vrrp_instance VI_2 {
    state BACKUP
    interface ens192
    virtual_router_id 52
    priority 100
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass planetek2024
    }
    virtual_ipaddress {
        ***********/24
    }
    track_script {
        chk_service
    }
    notify_master "/etc/keepalived/master.sh"
    notify_backup "/etc/keepalived/backup.sh"
    notify_fault "/etc/keepalived/fault.sh"
}
