#!/bin/bash

# Script eseguito quando si verifica un fault
# Ferma i servizi e registra l'evento

LOG_FILE="/var/log/keepalived/keepalived.log"

# Funzione di logging
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - FAULT - $1" >> "$LOG_FILE"
}

log_message "FAULT detected - Stopping services"

# Ferma entrambi i servizi
systemctl stop haproxy 2>/dev/null
systemctl stop bunkerweb 2>/dev/null

log_message "Services stopped due to FAULT"
