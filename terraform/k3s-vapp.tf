resource "vcd_vapp" "k3s_vapp" {
  name        = "K3S"
  org         = var.vcd_org
  vdc         = var.vcd_vdc
  description = "vApp per i server K3S"
}

# Aggiungi la rete rancher-lan alla vApp K3S
resource "vcd_vapp_org_network" "k3s_vapp_rancher_lan_network" {
  org              = var.vcd_org
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.k3s_vapp.name
  org_network_name = data.vcd_network_isolated_v2.rancher_lan.name # Assumendo che rancher_lan sia definita in discover-networks.tf
}

// Istruzioni per l'importazione della vApp K3S:
// terraform import vcd_vapp.k3s_vapp PLANETEKS001.Planevdc25_Multisite.K3S
// Verificare che il nome della vApp ('K3S') corrisponda a quello in vCloud Director.