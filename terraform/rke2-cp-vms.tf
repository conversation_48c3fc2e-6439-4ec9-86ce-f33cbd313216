// RKE2 Control Plane VMs - Da importare (se non già gestite)

// Assumiamo che ci siano 3 nodi control plane, 'count' è impostato di conseguenza.
// Se queste VM non sono nella vApp "rke2", 'vapp_name' andr<PERSON> modificato
// o si dovrà considerare l'uso di 'vcd_vm' se sono standalone.

locals {
  rke2_cp_ips = [
    "************", # rke2-cp-1
    "************", # rke2-cp-2
    "************", # rke2-cp-3
  ]
}

resource "vcd_vapp_vm" "rke2_cp_vm" {
  count     = 3 // Ripristino a 3 VM
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = vcd_vapp.rke2_vapp.name // Associa alla vApp rke2 esistente

  name      = format("rke2-cp-%d", count.index + 1) // Nome logico in Terraform
  // computer_name = format("NOME_COMPUTER_RKE2_CP_%d", count.index + 1) // Opzionale

  // Attributi da popolare con 'terraform import'
  // NOTA: vapp_template_id NON deve essere specificato per VM importate esistenti
  // vapp_template_id = data.vcd_catalog_vapp_template.ubuntu_template.id // Solo per CREARE nuove VM
  cpus   = 8
  memory = 16384 // 16GB RAM
  
  // Parametri di default per evitare modifiche non necessarie
  accept_all_eulas         = true
  power_on                 = true
  prevent_update_power_off = false
  network {
    type                 = "org"
    name                 = "rancher-lan" # Assicurati che questo sia il nome corretto della Org Network
    ip_allocation_mode = "MANUAL"
    ip                   = local.rke2_cp_ips[count.index]
    is_primary           = true
  }
  // customization { ... }

  depends_on = [ vcd_vapp.rke2_vapp ]
}

// Istruzioni per l'importazione:
// Esempio per il primo nodo:
// terraform import vcd_vapp_vm.rke2_cp_vm[0] PLANETEKS001.Planevdc25_Multisite.rke2.rke2-cp-1
// terraform import vcd_vapp_vm.rke2_cp_vm[1] PLANETEKS001.Planevdc25_Multisite.rke2.rke2-cp-2
// terraform import vcd_vapp_vm.rke2_cp_vm[2] PLANETEKS001.Planevdc25_Multisite.rke2.rke2-cp-3
// Verificare che i nomi delle VM ('rke2-cp-1', ecc.) e della vApp ('rke2') corrispondano a quelli in vCloud Director.