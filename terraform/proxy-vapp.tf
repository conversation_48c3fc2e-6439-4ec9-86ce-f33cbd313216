resource "vcd_vapp" "proxy_vapp" {
  name        = "PROXY"
  org         = var.vcd_org
  vdc         = var.vcd_vdc
  description = "vApp per i server proxy"
}

resource "vcd_vapp_org_network" "proxy_vapp_revproxy_lan_network" {
  org              = var.vcd_org
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.proxy_vapp.name
  org_network_name = "revproxy-lan"
}

resource "vcd_vapp_org_network" "proxy_vapp_rancher_lan_network" {
  org              = var.vcd_org
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.proxy_vapp.name
  org_network_name = "rancher-lan"
}

# Se hai altre reti da connettere alla vApp PROXY, aggiungi altre risorse vcd_vapp_org_network qui.

// Istruzioni per l'importazione della vApp PROXY:
// terraform import vcd_vapp.proxy_vapp PLANETEKS001.Planevdc25_Multisite.PROXY
// Verificare che il nome della vApp ('PROXY') corrisponda a quello in vCloud Director.