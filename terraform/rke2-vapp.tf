resource "vcd_vapp" "rke2_vapp" {
  name        = "rke2"
  org         = var.vcd_org
  vdc         = var.vcd_vdc
  description = "vApp per i nodi RKE2"
}

# Aggiungi la rete rancher-lan alla vApp rke2
resource "vcd_vapp_org_network" "rke2_vapp_rancher_lan_network" {
  org              = var.vcd_org
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.rke2_vapp.name
  org_network_name = data.vcd_network_isolated_v2.rancher_lan.name # Assumendo che rancher_lan sia definita in discover-networks.tf
}

// Istruzioni per l'importazione della vApp RKE2:
// terraform import vcd_vapp.rke2_vapp PLANETEKS001.Planevdc25_Multisite.rke2
// Verificare che il nome della vApp ('rke2') corrisponda a quello in vCloud Director.