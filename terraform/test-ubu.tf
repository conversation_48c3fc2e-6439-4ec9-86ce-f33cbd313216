# /* # Crea una vApp di test
# resource "vcd_vapp" "test_vapp" {
#   name = "terraform-test"
#   org  = var.vcd_org
#   vdc  = var.vcd_vdc
  
#   description = "Test vApp per Terraform"
# } */

# # Aggiungi la rete alla vApp
# resource "vcd_vapp_org_network" "test_network" {
#   org              = var.vcd_org
#   vdc              = var.vcd_vdc
#   vapp_name        = vcd_vapp.test_vapp.name
#   org_network_name = "rancher-lan"
# }

# Prima VM di test usando il template rke2-cp-x
resource "vcd_vapp_vm" "test_vm_2" {
  org         = var.vcd_org        # ← Aggiungi org
  vdc         = var.vcd_vdc        # ← Aggiungi vdc
  vapp_name   = vcd_vapp.test_vapp.name
  name        = "test-ubuntu"
  computer_name = "test-ubuntu"
  
  vapp_template_id = data.vcd_catalog_vapp_template.ubuntu_template.id
  
  cpus   = 4
  memory = 8192  # 8GB RAM

  # Il template ha già il disco configurato, non specificarlo

  # Usa la rete rancher-lan (ISOLATED)
  network {
    type               = "org"
    name               = "rancher-lan"
    ip_allocation_mode = "MANUAL"
    ip                 = "************"  
  }
network {
    type               = "org" # Assumendo che 'storage' sia una Org VDC network
    name               = "storage"
    ip_allocation_mode = "POOL" # o "MANUAL" se hai un IP specifico da assegnare
    # ip                 = "IP_SPECIFICO_SE_MANUAL" # Decommenta e imposta se usi MANUAL
    is_primary         = false # La prima rete è già primaria
  }
  customization {
    force                      = false
    change_sid                 = false
    allow_local_admin_password = true
    auto_generate_password     = false
    admin_password            = var.vm_admin_password
  }

  depends_on = [vcd_vapp.test_vapp, vcd_vapp_org_network.test_network]
}

# Output con info della VM creata
output "test_vm_2_info" {
  value = {
    vm_name   = vcd_vapp_vm.test_vm_2.name
    vapp_name = vcd_vapp.test_vapp.name
    vm_ip     = try(vcd_vapp_vm.test_vm_2.network[0].ip, "N/A")
    vm_status = vcd_vapp_vm.test_vm_2.status
  }
}