// K3S VMs - Da importare

resource "vcd_vapp_vm" "k3s_vm_1" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = vcd_vapp.k3s_vapp.name // Associa alla vApp K3S

  name      = "k3s-1" // Nome logico in Terraform
  // computer_name = "NOME_COMPUTER_K3S_1" // Opzionale

  // Attributi da popolare con 'terraform import'
  // vapp_template_id = data.vcd_catalog_vapp_template.NOME_TEMPLATE.id // Specifica il template se vuoi gestirlo
  cpus   = 4
  memory = 8192 // 8GB RAM
  network {
    type                 = "org"
    name                 = "rancher-lan" # Assicurati che questo sia il nome corretto della Org Network
    ip_allocation_mode = "MANUAL"
    ip                   = "************"
    is_primary           = true
  }
  // customization { ... }

  depends_on = [ vcd_vapp.k3s_vapp ]
}

resource "vcd_vapp_vm" "k3s_vm_2" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = vcd_vapp.k3s_vapp.name // Associa alla vApp K3S

  name      = "k3s-2" // Nome logico in Terraform
  // computer_name = "NOME_COMPUTER_K3S_2" // Opzionale

  // Attributi da popolare con 'terraform import'
  // vapp_template_id = data.vcd_catalog_vapp_template.NOME_TEMPLATE.id // Specifica il template se vuoi gestirlo
  cpus   = 4
  memory = 8192 // 8GB RAM
  network {
    type                 = "org"
    name                 = "rancher-lan" # Assicurati che questo sia il nome corretto della Org Network
    ip_allocation_mode = "MANUAL"
    ip                   = "************"
    is_primary           = true
  }

  depends_on = [ vcd_vapp.k3s_vapp ]
}

resource "vcd_vapp_vm" "k3s_vm_3" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = vcd_vapp.k3s_vapp.name // Associa alla vApp K3S

  name      = "k3s-3" // Nome logico in Terraform
  // computer_name = "NOME_COMPUTER_K3S_3" // Opzionale

  // Attributi da popolare con 'terraform import'
  // vapp_template_id = data.vcd_catalog_vapp_template.NOME_TEMPLATE.id // Specifica il template se vuoi gestirlo
  cpus   = 4
  memory = 8192 // 8GB RAM
  network {
    type                 = "org"
    name                 = "rancher-lan" # Assicurati che questo sia il nome corretto della Org Network
    ip_allocation_mode = "MANUAL"
    ip                   = "************"
    is_primary           = true
  }

  depends_on = [ vcd_vapp.k3s_vapp ]
}

// Istruzioni per l'importazione:
// terraform import vcd_vapp_vm.k3s_vm_1 PLANETEKS001.Planevdc25_Multisite.K3S.k3s-1
// terraform import vcd_vapp_vm.k3s_vm_2 PLANETEKS001.Planevdc25_Multisite.K3S.k3s-2
// terraform import vcd_vapp_vm.k3s_vm_3 PLANETEKS001.Planevdc25_Multisite.K3S.k3s-3
// Verificare che i nomi delle VM ('k3s-1', 'k3s-2', 'k3s-3') e della vApp ('K3S') corrispondano a quelli in vCloud Director.