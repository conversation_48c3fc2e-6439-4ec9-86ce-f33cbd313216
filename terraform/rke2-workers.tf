resource "vcd_vapp_vm" "rke2_worker" {
  count       = 20 # Numero di worker RKE2 iniziali
  org         = var.vcd_org
  vdc         = var.vcd_vdc
  vapp_name   = vcd_vapp.rke2_vapp.name

  name        = format("rke2-wk-%d", count.index + 1)
  computer_name = format("rke2-wk-%d", count.index + 1)

  vapp_template_id = data.vcd_catalog_vapp_template.ubuntu_template.id # Assumendo che ubuntu_template sia il template corretto

  cpus   = 32
  memory = 65536 # 64GB RAM

  network {
    type               = "org"
    name               = "rancher-lan" # Rete primaria
    ip_allocation_mode = "MANUAL"
    ip                 = format("172.16.20.%d", 31 + count.index)
    is_primary         = true
  }

  # Se necessario, aggiungere qui la seconda interfaccia per la rete "storage" con ip_allocation_mode = "POOL"
  # network {
  #   type               = "org"
  #   name               = "storage"
  #   ip_allocation_mode = "POOL"
  #   is_primary         = false
  # }

  customization {
    force                      = false
    change_sid                 = false
    allow_local_admin_password = true
    auto_generate_password     = false
    admin_password            = var.vm_admin_password
  }

  depends_on = [
    vcd_vapp.rke2_vapp,
    vcd_vapp_org_network.rke2_vapp_rancher_lan_network
    # Aggiungere qui la dipendenza dalla rete storage per la vApp rke2 se si aggiunge la seconda interfaccia
    # vcd_vapp_org_network.rke2_vapp_storage_network 
  ]
}

# ========================================
# DISCHI AGGIUNTIVI PER I WORKER RKE2
# ========================================

resource "vcd_vm_internal_disk" "rke2_worker_data_disk" {
  count = 20  # Un disco per ogni worker

  org             = var.vcd_org
  vdc             = var.vcd_vdc
  vapp_name       = vcd_vapp.rke2_vapp.name
  vm_name         = vcd_vapp_vm.rke2_worker[count.index].name
  bus_type        = "paravirtual"
  size_in_mb      = "1024000"  # 1TB = 1024GB = 1024000MB
  bus_number      = 1          # Bus separato dal disco OS
  unit_number     = 0          # Primo disco sul bus 1
  allow_vm_reboot = false      # Non riavviare durante l'aggiunta

  depends_on = [vcd_vapp_vm.rke2_worker]
}

output "rke2_worker_details_initial" {
  value = [for i in range(length(vcd_vapp_vm.rke2_worker)) : {
    name = vcd_vapp_vm.rke2_worker[i].name
    ip   = vcd_vapp_vm.rke2_worker[i].network[0].ip
    data_disk_size_gb = 1024
  }]
}