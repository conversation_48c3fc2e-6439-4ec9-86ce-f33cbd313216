// Proxy VMs - Da importare

resource "vcd_vapp_vm" "proxy_vm_1" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = vcd_vapp.proxy_vapp.name // Associa alla vApp PROXY

  name      = "proxy-1" // Nome logico in Terraform
  // computer_name = "NOME_COMPUTER_PROXY_1" // Opzionale

  // Attributi da popolare con 'terraform import'
  // vapp_template_id = data.vcd_catalog_vapp_template.NOME_TEMPLATE.id // Specifica il template se vuoi gestirlo
  cpus   = 4
  memory = 4096 // 4GB RAM

  network {
    type                 = "org"
    name                 = "revproxy-lan"
    ip_allocation_mode = "MANUAL"
    ip                   = "************"
    is_primary           = true
  }

  network {
    type                 = "org"
    name                 = "rancher-lan"
    ip_allocation_mode = "MANUAL"
    ip                   = "***********"
    is_primary           = false
  }
  // customization { ... }

  depends_on = [ vcd_vapp.proxy_vapp ]
}

resource "vcd_vapp_vm" "proxy_vm_2" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = vcd_vapp.proxy_vapp.name // Associa alla vApp PROXY

  name      = "proxy-2" // Nome logico in Terraform
  // computer_name = "NOME_COMPUTER_PROXY_2" // Opzionale

  // Attributi da popolare con 'terraform import'
  // vapp_template_id = data.vcd_catalog_vapp_template.NOME_TEMPLATE.id // Specifica il template se vuoi gestirlo
  cpus   = 4
  memory = 4096 // 4GB RAM

  network {
    type                 = "org"
    name                 = "revproxy-lan"
    ip_allocation_mode = "MANUAL"
    ip                   = "************"
    is_primary           = true
  }

  network {
    type                 = "org"
    name                 = "rancher-lan"
    ip_allocation_mode = "MANUAL"
    ip                   = "***********"
    is_primary           = false
  }
  // customization { ... }

  depends_on = [ vcd_vapp.proxy_vapp ]
}

// Istruzioni per l'importazione:
// terraform import vcd_vapp_vm.proxy_vm_1 PLANETEKS001.Planevdc25_Multisite.PROXY.proxy-1
// terraform import vcd_vapp_vm.proxy_vm_2 PLANETEKS001.Planevdc25_Multisite.PROXY.proxy-2
// Verificare che i nomi delle VM ('proxy-1', 'proxy-2') e della vApp ('PROXY') corrispondano a quelli in vCloud Director.