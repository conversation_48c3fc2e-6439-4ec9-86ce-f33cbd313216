// Fortinet VMs - Da importare

// Se la VM è in una vApp, `vapp_name` deve essere specificato.
resource "vcd_vapp_vm" "fortinet_vm_1" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = "FORTIGATE"

  name                     = "FortiGate01"
  accept_all_eulas         = true
  power_on                 = true
  prevent_update_power_off = false
  cpus                     = 2
  memory                   = 4096

  // Configurazioni di rete basate sullo stato reale importato
  // L'ordine deve corrispondere esattamente a quello in vCloud Director
  
  // Interfaccia 0 - Diretta_Planetek (primaria)
  network {
    type               = "org"
    name               = "Diretta_Planetek_194_21_37_96_sl27"
    ip_allocation_mode = "POOL"
    ip                 = "************"
    is_primary         = true
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 1 - revproxy-lan
  network {
    type               = "org"
    name               = "revproxy-lan"
    ip_allocation_mode = "MANUAL"
    ip                 = "**************"
    is_primary         = false
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 2 - rancher-lan
  network {
    type               = "org"
    name               = "rancher-lan"
    ip_allocation_mode = "MANUAL"
    ip                 = "*************"
    is_primary         = false
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 3 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 4 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 5 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 6 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 7 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 8 - rete_heartbeat (connessa)
  network {
    type               = "org"
    name               = "rete_heartbeat"
    ip_allocation_mode = "MANUAL"
    ip                 = "***********"
    is_primary         = false
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 9 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }
}

resource "vcd_vapp_vm" "fortinet_vm_2" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = "FORTIGATE"

  name                     = "FortiGate02"
  accept_all_eulas         = true
  power_on                 = true
  prevent_update_power_off = false
  cpus                     = 2
  memory                   = 4096

  // Configurazioni di rete basate sullo stato reale importato
  // L'ordine deve corrispondere esattamente a quello in vCloud Director
  
  // Interfaccia 0 - Diretta_Planetek (primaria)
  network {
    type               = "org"
    name               = "Diretta_Planetek_194_21_37_96_sl27"
    ip_allocation_mode = "POOL"
    ip                 = "************"
    is_primary         = true
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 1 - revproxy-lan
  network {
    type               = "org"
    name               = "revproxy-lan"
    ip_allocation_mode = "MANUAL"
    ip                 = "**************"
    is_primary         = false
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 2 - rancher-lan
  network {
    type               = "org"
    name               = "rancher-lan"
    ip_allocation_mode = "MANUAL"
    ip                 = "*************"
    is_primary         = false
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 3 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 4 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 5 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 6 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 7 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }

  // Interfaccia 8 - rete_heartbeat (connessa)
  network {
    type               = "org"
    name               = "rete_heartbeat"
    ip_allocation_mode = "MANUAL"
    ip                 = "***********"
    is_primary         = false
    adapter_type       = "VMXNET3"
    connected          = true
  }

  // Interfaccia 9 - none
  network {
    type               = "none"
    adapter_type       = "VMXNET3"
    connected          = false
    ip_allocation_mode = "NONE"
    is_primary         = false
  }
}

// Istruzioni per l'importazione:
// terraform import vcd_vapp_vm.fortinet_vm_1 PLANETEKS001.Planevdc25_Multisite.FORTIGATE.FortiGate01
// terraform import vcd_vapp_vm.fortinet_vm_2 PLANETEKS001.Planevdc25_Multisite.FORTIGATE.FortiGate02
// Verificare che i nomi delle VM ('FortiGate01', 'FortiGate02') e della vApp ('FORTIGATE') corrispondano a quelli in vCloud Director.