resource "vcd_vapp" "fortigate_vapp" {
  name        = "FORTIGATE"
  org         = var.vcd_org
  vdc         = var.vcd_vdc
  # description e power_on rimossi per allinearsi allo stato reale
}

resource "vcd_vapp_org_network" "fortigate_vapp_diretta_planetek_network" {
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.fortigate_vapp.name
  org_network_name = "Diretta_Planetek_194_21_37_96_sl27"
}

resource "vcd_vapp_org_network" "fortigate_vapp_revproxy_lan_network" {
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.fortigate_vapp.name
  org_network_name = "revproxy-lan"
}

resource "vcd_vapp_org_network" "fortigate_vapp_rancher_lan_network" {
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.fortigate_vapp.name
  org_network_name = "rancher-lan"
}

resource "vcd_vapp_org_network" "fortigate_vapp_rete_heartbeat_network" {
  vdc              = var.vcd_vdc
  vapp_name        = vcd_vapp.fortigate_vapp.name
  org_network_name = "rete_heartbeat"
}

// Istruzioni per l'importazione della vApp FORTIGATE:
// terraform import vcd_vapp.fortigate_vapp PLANETEKS001.Planevdc25_Multisite.FORTIGATE
// Verificare che il nome della vApp ('FORTIGATE') corrisponda a quello in vCloud Director.