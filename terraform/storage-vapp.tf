# resource "vcd_vapp" "storage_vapp" {
#   name        = "STORAGE"
#   org         = var.vcd_org
#   vdc         = var.vcd_vdc
#   description = "vApp per i server Ceph e monitor"
#   # power_off_on_destroy = true # Attributo non valido, rimosso
# }

# Aggiungi la rete rancher-lan alla vApp STORAGE
# resource "vcd_vapp_org_network" "storage_vapp_rancher_lan_network" {
#   org              = var.vcd_org
#   vdc              = var.vcd_vdc
#   vapp_name        = "STORAGE"
#   org_network_name = data.vcd_network_isolated_v2.rancher_lan.name # Assumendo che rancher_lan sia definita in discover-networks.tf
# }

# Aggiungi la rete storage alla vApp STORAGE
# resource "vcd_vapp_org_network" "storage_vapp_storage_network" {
#   org              = var.vcd_org
#   vdc              = var.vcd_vdc
#   vapp_name        = "STORAGE"
#   org_network_name = data.vcd_network_isolated_v2.storage_network.name # Assumendo che storage_network sia definita in discover-networks.tf
# }