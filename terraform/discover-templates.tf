# Catalogo e template trovati nell'interfaccia
data "vcd_catalog" "private_catalog" {
  org  = var.vcd_org
  name = "Catalogo Privato"
}

data "vcd_catalog_vapp_template" "ubuntu_template" {
  org         = var.vcd_org
  catalog_id  = data.vcd_catalog.private_catalog.id
  name        = "rke2-cp-x"
}


# Output per verificare template e edge gateway
output "template_found" {
  value = {
    catalog_name    = data.vcd_catalog.private_catalog.name
    template_name   = data.vcd_catalog_vapp_template.ubuntu_template.name
    template_id     = data.vcd_catalog_vapp_template.ubuntu_template.id
    description     = data.vcd_catalog_vapp_template.ubuntu_template.description
  }
}

