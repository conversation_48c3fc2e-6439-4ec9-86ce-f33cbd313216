# variable "ceph_monitor_ips" {
#   description = "Lista di indirizzi IP per i monitor Ceph sulla rete rancher-lan"
#   type        = list(string)
#   default     = ["*************", "*************", "*************"]
# }
#
# variable "ceph_monitor_names" {
#   description = "Lista dei nomi per i monitor Ceph"
#   type        = list(string)
#   default     = ["ceph-4", "ceph-5", "ceph-6"]
# }
#
# resource "vcd_vapp_vm" "ceph_monitor" {
#   count       = 3 # Numero di monitor Ceph
#   org         = var.vcd_org
#   vdc         = var.vcd_vdc
#   vapp_name   = vcd_vapp.storage_vapp.name
#
#   name        = var.ceph_monitor_names[count.index]
#   computer_name = var.ceph_monitor_names[count.index]
#
#   vapp_template_id = data.vcd_catalog_vapp_template.ubuntu_template.id
#
#   cpus   = 4
#   memory = 8192 # 8GB RAM
#
#   network {
#     type               = "org"
#     name               = "rancher-lan" # Rete primaria
#     ip_allocation_mode = "MANUAL"
#     ip                 = var.ceph_monitor_ips[count.index]
#     is_primary         = true
#   }
#
#   network {
#     type               = "org"
#     name               = "storage" # Seconda rete
#     ip_allocation_mode = "POOL"
#     is_primary         = false
#   }
#
#   customization {
#     force                      = false
#     change_sid                 = false
#     allow_local_admin_password = true
#     auto_generate_password     = false
#     admin_password            = var.vm_admin_password
#   }
#
#   depends_on = [
#     vcd_vapp.storage_vapp,
#     vcd_vapp_org_network.storage_vapp_rancher_lan_network,
#     vcd_vapp_org_network.storage_vapp_storage_network
#   ]
# }
#
# output "ceph_monitor_details" {
#   value = [for i in range(3) : {
#     name    = vcd_vapp_vm.ceph_monitor[i].name
#     ip_rancher_lan = vcd_vapp_vm.ceph_monitor[i].network[0].ip
#     ip_storage_lan = try(vcd_vapp_vm.ceph_monitor[i].network[1].ip, "N/A (POOL)")
#   }]
# }