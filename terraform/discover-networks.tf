# Rete DIRECT
data "vcd_network_direct" "diretta_planetek" {
  org  = var.vcd_org
  vdc  = var.vcd_vdc
  name = "Diretta_Planetek_194_21_37_96_sl27"
}

# Reti ISOLATED
data "vcd_network_isolated_v2" "rancher_lan" {
  org  = var.vcd_org
  owner_id  = data.vcd_org_vdc.current.id
  name = "rancher-lan"
}

data "vcd_network_isolated_v2" "rancher_lan_old" {
  org  = var.vcd_org
  owner_id  = data.vcd_org_vdc.current.id
  name = "rancher-lan-old"
}

data "vcd_network_isolated_v2" "rete_heartbeat" {
  org  = var.vcd_org
  owner_id  = data.vcd_org_vdc.current.id
  name = "rete_heartbeat"
}

data "vcd_network_isolated_v2" "revproxy_lan" {
  org  = var.vcd_org
  owner_id  = data.vcd_org_vdc.current.id
  name = "revproxy-lan"
}
# data "vcd_network_isolated_v2" "storage_network" {
#   org       = var.vcd_org
#   owner_id  = data.vcd_org_vdc.current.id
#   name      = "storage"
# }

# Output per vedere tutte le reti trovate
output "available_networks" {
  value = {
    diretta_planetek = {
      name = data.vcd_network_direct.diretta_planetek.name
      type = "DIRECT"
    }
    rancher_lan = {
      name = data.vcd_network_isolated_v2.rancher_lan.name
      type = "ISOLATED"
    }
    rancher_lan_old = {
      name = data.vcd_network_isolated_v2.rancher_lan_old.name
      type = "ISOLATED"
    }
    rete_heartbeat = {
      name = data.vcd_network_isolated_v2.rete_heartbeat.name
      type = "ISOLATED"
    }
    revproxy_lan = {
      name = data.vcd_network_isolated_v2.revproxy_lan.name
      type = "ISOLATED"
    }
    # storage_network = { # Rimosso riferimento alla rete storage
    #   name = data.vcd_network_isolated_v2.storage_network.name
    #   type = "ISOLATED"
    # }
  }
}