# ========================================
# VARIABILI PER I SERVER CEPH
# ========================================

# variable "ceph_server_ips" {
#   description = "Lista di indirizzi IP per i server Ceph sulla rete rancher-lan"
#   type        = list(string)
#   default     = ["*************", "*************", "*************"]
# }
#
# variable "ceph_server_names" {
#   description = "Lista dei nomi per i server Ceph"
#   type        = list(string)
#   default     = ["ceph-1", "ceph-2", "ceph-3"]
# }
#
# variable "storage_profile" {
#   description = "Storage profile per i dischi OSD Ceph"
#   type        = string
#   default     = "*"  # Usa storage profile di default
# }

# ========================================
# SERVER CEPH - VM BASE
# ========================================

# resource "vcd_vapp_vm" "ceph_server" {
#   count       = 3
#   org         = var.vcd_org
#   vdc         = var.vcd_vdc
#   vapp_name   = vcd_vapp.storage_vapp.name
#
#   name          = var.ceph_server_names[count.index]
#   computer_name = var.ceph_server_names[count.index]
#
#   vapp_template_id = data.vcd_catalog_vapp_template.ubuntu_template.id
#
#   cpus   = 16
#   memory = 65536  # 64GB RAM
#
#   # Rete primaria per accesso client
#   network {
#     type               = "org"
#     name               = "rancher-lan"
#     ip_allocation_mode = "MANUAL"
#     ip                 = var.ceph_server_ips[count.index]
#     is_primary         = true
#   }
#
#   # Rete secondaria per traffico cluster Ceph
#   network {
#     type               = "org"
#     name               = "storage"
#     ip_allocation_mode = "POOL"
#     is_primary         = false
#   }
#
#   # Customization del sistema operativo
#   customization {
#     force                      = false
#     change_sid                 = false
#     allow_local_admin_password = true
#     auto_generate_password     = false
#     admin_password            = var.vm_admin_password
#   }
#
#   depends_on = [
#     vcd_vapp.storage_vapp,
#     vcd_vapp_org_network.storage_vapp_storage_network,
#     vcd_vapp_org_network.storage_vapp_rancher_lan_network
#   ]
# }

# ========================================
# DISCHI OSD CEPH - DISCHI INTERNI
# ========================================

# resource "vcd_vm_internal_disk" "ceph_osd_disks" {
#   count = length(var.ceph_server_names) * 4  # 3 server x 4 OSD = 12 dischi totali
#
#   org             = var.vcd_org              # ← Aggiungi org
#   vdc             = var.vcd_vdc              # ← Aggiungi vdc
#   vapp_name       = vcd_vapp.storage_vapp.name
#   vm_name         = var.ceph_server_names[floor(count.index / 4)]
#   bus_type        = "paravirtual"
#   size_in_mb      = "2048000"  # 2TB per ogni OSD
#   bus_number      = 1          # Bus separato dal disco OS
#   unit_number     = count.index % 4  # 0,1,2,3 per ogni server
#   #storage_profile = var.storage_profile
#   allow_vm_reboot = false      # Non riavviare durante l'aggiunta
#
#   depends_on = [vcd_vapp_vm.ceph_server]
# }

# ========================================
# OUTPUT INFORMATIVI
# ========================================

# output "ceph_server_details" {
#   description = "Dettagli dei server Ceph creati"
#   value = [for i in range(3) : {
#     name           = vcd_vapp_vm.ceph_server[i].name
#     ip_rancher_lan = vcd_vapp_vm.ceph_server[i].network[0].ip
#     ip_storage_lan = try(vcd_vapp_vm.ceph_server[i].network[1].ip, "POOL - IP dinamico")
#     cpus           = vcd_vapp_vm.ceph_server[i].cpus
#     memory_gb      = vcd_vapp_vm.ceph_server[i].memory / 1024
#     status         = vcd_vapp_vm.ceph_server[i].status
#   }]
# }
#
# output "ceph_disk_mapping" {
#   description = "Mapping dei dischi OSD per server"
#   value = {
#     for i in range(length(var.ceph_server_names) * 4) :
#     "osd-${i}" => {
#       vm_name     = var.ceph_server_names[floor(i / 4)]
#       unit_number = i % 4
#       size_gb     = 2000
#       device_path = "/dev/sd${substr("bcdefgh", i % 4, 1)}"  # /dev/sdb, /dev/sdc, etc.
#     }
#   }
# }
#
# output "ceph_cluster_summary" {
#   description = "Riepilogo del cluster Ceph"
#   value = {
#     total_servers    = length(var.ceph_server_names)
#     total_osd_disks  = length(var.ceph_server_names) * 4
#     total_raw_storage_tb = (length(var.ceph_server_names) * 4 * 2)  # TB totali
#     usable_storage_tb = (length(var.ceph_server_names) * 4 * 2) / 3  # Con replication factor 3
#     server_specs = {
#       cpu_per_server    = 16
#       ram_per_server_gb = 64
#       osd_per_server    = 4
#       osd_size_gb       = 2000
#     }
#   }
# }