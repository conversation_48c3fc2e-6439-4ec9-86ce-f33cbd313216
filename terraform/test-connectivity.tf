# Data source per testare la connettività
data "vcd_org" "current" {
  name = var.vcd_org
}

data "vcd_org_vdc" "current" {
  org  = var.vcd_org
  name = var.vcd_vdc
}

# Cerca le VM standalone (senza vApp)
data "vcd_vapp_vm" "proxy_1" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = ""  # VM standalone - vApp vuota
  name      = "proxy-1"
}

/*
data "vcd_vapp_vm" "proxy_2" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = ""
  name      = "proxy-2"
}
*/
data "vcd_vapp_vm" "k3s_1" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = ""
  name      = "k3s-1"
}

data "vcd_vapp_vm" "k3s_2" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = ""
  name      = "k3s-2"
}

data "vcd_vapp_vm" "k3s_3" {
  org       = var.vcd_org
  vdc       = var.vcd_vdc
  vapp_name = ""
  name      = "k3s-3"
}

# Output per verificare che funzioni
output "org_info" {
  value = {
    org_name     = data.vcd_org.current.name
    org_fullname = data.vcd_org.current.full_name
    vdc_name     = data.vcd_org_vdc.current.name
    org_id       = data.vcd_org.current.id
    vdc_id       = data.vcd_org_vdc.current.id
  }
}

output "existing_vms" {
  value = {
    proxy_1 = {
      name   = data.vcd_vapp_vm.proxy_1.name
      status = data.vcd_vapp_vm.proxy_1.status
      cpus   = data.vcd_vapp_vm.proxy_1.cpus
      memory = data.vcd_vapp_vm.proxy_1.memory
    }
    /* proxy_2 = {
      name   = data.vcd_vapp_vm.proxy_2.name
      status = data.vcd_vapp_vm.proxy_2.status
      cpus   = data.vcd_vapp_vm.proxy_2.cpus
      memory = data.vcd_vapp_vm.proxy_2.memory
    } */
    k3s_1 = {
      name   = data.vcd_vapp_vm.k3s_1.name
      status = data.vcd_vapp_vm.k3s_1.status
      cpus   = data.vcd_vapp_vm.k3s_1.cpus
      memory = data.vcd_vapp_vm.k3s_1.memory
    }
    k3s_2 = {
      name   = data.vcd_vapp_vm.k3s_2.name
      status = data.vcd_vapp_vm.k3s_2.status
      cpus   = data.vcd_vapp_vm.k3s_2.cpus
      memory = data.vcd_vapp_vm.k3s_2.memory
    }
    k3s_3 = {
      name   = data.vcd_vapp_vm.k3s_3.name
      status = data.vcd_vapp_vm.k3s_3.status
      cpus   = data.vcd_vapp_vm.k3s_3.cpus
      memory = data.vcd_vapp_vm.k3s_3.memory
    }
  }
}
